<x-filament-panels::page>
    @vite('resources/css/app.css')

    <!-- Header com ações -->
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON> de Leads</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1"><PERSON><PERSON><PERSON> os leads entre as colunas para alterar suas categorias</p>
        </div>
        <div class="flex gap-2">
            <x-filament::button
                href="{{ \App\Filament\Resources\Leads\LeadResource::getUrl('create') }}"
                icon="heroicon-o-plus"
                size="sm"
            >
                Novo Lead
            </x-filament::button>
            <x-filament::button
                wire:click="refreshData"
                icon="heroicon-o-arrow-path"
                size="sm"
                color="gray"
                wire:loading.attr="disabled"
                wire:target="refreshData"
            >
                <span wire:loading.remove wire:target="refreshData">Atualizar</span>
                <span wire:loading wire:target="refreshData">Atualizando...</span>
            </x-filament::button>
        </div>
    </div>

    <!-- Indicador de carregamento -->
    <div wire:loading wire:target="updateLeadCategory,refreshData" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3">
            <x-filament::loading-indicator class="h-5 w-5" />
            <span class="text-gray-900 dark:text-white">Processando...</span>
        </div>
    </div>

    <div
        x-data="kanbanBoard()"
        x-init="init()"
        class="overflow-x-auto bg-gray-100 dark:bg-gray-900 p-4 rounded-lg"
        wire:loading.class="opacity-50"
        wire:target="updateLeadCategory,refreshData"
    >
        <div class="flex gap-6 min-w-fit pb-6">
            @foreach($this->getCategorias() as $categoria)
                <div
                    class="flex-shrink-0 w-80 bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700"
                    x-data="{ categoryId: {{ $categoria->id }} }"
                >
                    <!-- Header da Coluna -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-2">
                            <div 
                                class="w-3 h-3 rounded-full" 
                                style="background-color: {{ $categoria->cor }}"
                            ></div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">
                                {{ $categoria->nome }}
                            </h3>
                            <span class="text-sm text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">
                                {{ $this->getLeadsForCategory($categoria->id)->count() }}
                            </span>
                        </div>
                    </div>

                    <!-- Drop Zone -->
                    <div
                        class="min-h-96 space-y-3 rounded-lg p-3 transition-all duration-200 border-2 border-transparent"
                        x-ref="dropzone_{{ $categoria->id }}"
                        @dragover.prevent="handleDragOver($event, {{ $categoria->id }})"
                        @drop.prevent="handleDrop($event, {{ $categoria->id }})"
                        @dragenter.prevent="handleDragEnter($event, {{ $categoria->id }})"
                        @dragleave.prevent="handleDragLeave($event, {{ $categoria->id }})"
                    >
                        @forelse($this->getLeadsForCategory($categoria->id) as $lead)
                            <div
                                class="bg-white dark:bg-gray-900 rounded-lg p-4 shadow-md border border-gray-200 dark:border-gray-700 cursor-move hover:shadow-lg hover:-translate-y-1 transition-all duration-200 mb-3"
                                draggable="true"
                                x-data="{ leadId: {{ $lead->id }} }"
                                @dragstart="handleDragStart($event, {{ $lead->id }})"
                                @dragend="handleDragEnd($event)"
                                @click.stop="@this.viewLead({{ $lead->id }})"
                                role="button"
                                tabindex="0"
                                aria-label="Lead: {{ $lead->titulo }}"
                                title="Clique para editar ou arraste para mover"
                            >
                                <!-- Título do Lead -->
                                <h4 class="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                                    {{ $lead->titulo }}
                                </h4>

                                <!-- Cliente -->
                                <div class="flex items-center gap-2 mb-2">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $lead->cliente->nome }}
                                    </span>
                                </div>

                                <!-- Valor e Prioridade -->
                                <div class="flex items-center justify-between">
                                    @if($lead->valor_estimado)
                                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                            R$ {{ number_format($lead->valor_estimado, 2, ',', '.') }}
                                        </span>
                                    @else
                                        <span></span>
                                    @endif

                                    <div class="flex items-center gap-1">
                                        <div 
                                            class="w-2 h-2 rounded-full" 
                                            style="background-color: {{ $lead->prioridade_cor }}"
                                            title="Prioridade: {{ ucfirst($lead->prioridade) }}"
                                        ></div>
                                        <span class="text-xs text-gray-500 capitalize">
                                            {{ $lead->prioridade }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Data de Follow-up -->
                                @if($lead->data_followup)
                                    <div class="flex items-center gap-1 mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                                        <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span class="text-xs text-gray-500">
                                            Follow-up: {{ $lead->data_followup->format('d/m/Y') }}
                                        </span>
                                    </div>
                                @endif
                            </div>
                        @empty
                            <!-- Estado vazio -->
                            <div class="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400">
                                <svg class="w-8 h-8 mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                                </svg>
                                <p class="text-sm">Nenhum lead nesta categoria</p>
                                <p class="text-xs mt-1">Arraste leads aqui para movê-los</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            @endforeach
        </div>
    </div>



    <script>
        function kanbanBoard() {
            return {
                draggedLeadId: null,
                draggedElement: null,
                currentDropZone: null,

                init() {
                    console.log('Kanban Board inicializado');
                },

                handleDragStart(event, leadId) {
                    this.draggedLeadId = leadId;
                    this.draggedElement = event.target;
                    event.dataTransfer.effectAllowed = 'move';

                    // Estilo do elemento sendo arrastado
                    event.target.classList.add('opacity-50', 'rotate-2', 'scale-105', 'z-50', 'shadow-2xl');

                    // Adiciona indicadores visuais a todas as zonas de drop
                    document.querySelectorAll('[x-ref^="dropzone_"]').forEach(zone => {
                        zone.classList.add('border-dashed', 'border-gray-300', 'bg-gray-50', 'dark:bg-gray-800/50');
                    });

                    // Adiciona placeholder visual
                    setTimeout(() => {
                        const placeholder = document.createElement('div');
                        placeholder.className = 'h-24 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-300 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 text-sm font-medium mb-3';
                        placeholder.textContent = 'Solte aqui para mover o lead';
                        placeholder.id = 'drag-placeholder';

                        // Remove placeholder anterior se existir
                        const existingPlaceholder = document.getElementById('drag-placeholder');
                        if (existingPlaceholder) {
                            existingPlaceholder.remove();
                        }
                    }, 10);
                },

                handleDragEnd(event) {
                    // Remove estilos do elemento arrastado
                    event.target.classList.remove('opacity-50', 'rotate-2', 'scale-105', 'z-50', 'shadow-2xl');

                    // Remove indicadores visuais de todas as zonas
                    document.querySelectorAll('[x-ref^="dropzone_"]').forEach(zone => {
                        zone.classList.remove('border-dashed', 'border-gray-300', 'bg-gray-50', 'dark:bg-gray-800/50', 'border-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20');
                    });

                    // Remove placeholder
                    const placeholder = document.getElementById('drag-placeholder');
                    if (placeholder) {
                        placeholder.remove();
                    }

                    this.draggedElement = null;
                    this.currentDropZone = null;
                },

                handleDragEnter(event, categoryId) {
                    event.preventDefault();
                    const dropZone = event.currentTarget;

                    if (this.currentDropZone !== dropZone) {
                        // Remove highlight da zona anterior
                        if (this.currentDropZone) {
                            this.currentDropZone.classList.remove('border-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20');
                        }

                        // Adiciona highlight à zona atual
                        dropZone.classList.add('border-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20');
                        this.currentDropZone = dropZone;

                        // Adiciona placeholder se não existir
                        if (!dropZone.querySelector('#drag-placeholder')) {
                            const placeholder = document.createElement('div');
                            placeholder.className = 'h-24 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-400 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 text-sm font-medium mb-3';
                            placeholder.textContent = 'Solte aqui para mover o lead';
                            placeholder.id = 'drag-placeholder';
                            dropZone.appendChild(placeholder);
                        }
                    }
                },

                handleDragOver(event, categoryId) {
                    event.preventDefault();
                    event.dataTransfer.dropEffect = 'move';
                },

                handleDragLeave(event, categoryId) {
                    // Só remove o highlight se realmente saiu da zona (não de um filho)
                    if (!event.currentTarget.contains(event.relatedTarget)) {
                        event.currentTarget.classList.remove('border-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20');

                        // Remove placeholder se existir
                        const placeholder = event.currentTarget.querySelector('#drag-placeholder');
                        if (placeholder) {
                            placeholder.remove();
                        }

                        if (this.currentDropZone === event.currentTarget) {
                            this.currentDropZone = null;
                        }
                    }
                },

                handleDrop(event, newCategoryId) {
                    event.preventDefault();

                    if (!this.draggedLeadId) return;

                    // Remove todos os highlights e placeholders
                    document.querySelectorAll('[x-ref^="dropzone_"]').forEach(zone => {
                        zone.classList.remove('border-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20');
                        const placeholder = zone.querySelector('#drag-placeholder');
                        if (placeholder) {
                            placeholder.remove();
                        }
                    });

                    // Mostra feedback de carregamento
                    const notification = document.createElement('div');
                    notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all';
                    notification.textContent = 'Movendo lead...';
                    document.body.appendChild(notification);

                    // Chama o método do Livewire para atualizar o lead
                    @this.updateLeadCategory(this.draggedLeadId, newCategoryId).then(() => {
                        notification.textContent = 'Lead movido com sucesso!';
                        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all';
                        setTimeout(() => {
                            if (document.body.contains(notification)) {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    if (document.body.contains(notification)) {
                                        document.body.removeChild(notification);
                                    }
                                }, 300);
                            }
                        }, 2000);
                    }).catch(() => {
                        notification.textContent = 'Erro ao mover lead';
                        notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all';
                        setTimeout(() => {
                            if (document.body.contains(notification)) {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    if (document.body.contains(notification)) {
                                        document.body.removeChild(notification);
                                    }
                                }, 300);
                            }
                        }, 3000);
                    });

                    this.draggedLeadId = null;
                }
            }
        }
    </script>
</x-filament-panels::page>
