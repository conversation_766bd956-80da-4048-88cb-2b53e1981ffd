<?php

namespace App\Filament\Pages;

use App\Models\Lead;
use App\Models\CategoriaLead;
use App\Filament\Resources\Leads\LeadResource;
use BackedEnum;
use Filament\Pages\Page;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Collection;

class LeadsKanban extends Page
{
    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedViewColumns;



    protected static ?string $navigationLabel = 'Kanban de Leads';

    protected static ?string $title = 'Kanban de Leads';

    protected static ?int $navigationSort = 2;

    public Collection $categorias;
    public Collection $leads;

    public function mount(): void
    {
        $this->categorias = CategoriaLead::ordenadas();
        $this->leads = Lead::with(['cliente', 'categoriaLead'])
            ->orderBy('posicao')
            ->get()
            ->groupBy('categoria_lead_id');
    }

    public function updateLeadCategory($leadId, $newCategoryId, $newPosition = 0): void
    {
        $lead = Lead::find($leadId);

        if ($lead) {
            $oldCategoryId = $lead->categoria_lead_id;

            $lead->update([
                'categoria_lead_id' => $newCategoryId,
                'posicao' => $newPosition
            ]);

            // Reordenar leads na categoria antiga e nova
            if ($oldCategoryId !== $newCategoryId) {
                $this->reorderLeadsInCategory($oldCategoryId);
                $this->reorderLeadsInCategory($newCategoryId);
            }

            // Recarregar dados
            $this->mount();

            // Enviar notificação de sucesso
            $this->dispatch('lead-updated', [
                'message' => "Lead '{$lead->titulo}' movido para {$lead->categoriaLead->nome}"
            ]);
        }
    }

    public function refreshData(): void
    {
        $this->mount();
    }

    public function viewLead($leadId): void
    {
        $this->redirect(LeadResource::getUrl('edit', ['record' => $leadId]));
    }

    private function reorderLeadsInCategory($categoryId): void
    {
        $leads = Lead::where('categoria_lead_id', $categoryId)
            ->orderBy('posicao')
            ->get();

        foreach ($leads as $index => $lead) {
            $lead->update(['posicao' => $index + 1]);
        }
    }

    public function getLeadsForCategory($categoryId): Collection
    {
        return $this->leads->get($categoryId, collect());
    }

    public function getView(): string
    {
        return 'filament.pages.leads-kanban';
    }
}
